/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Objects;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoUserControllerTest {

    @Mock
    private ICrescendoUserService crescendoUserService;

    @InjectMocks
    private SalesforceCrescendoUserController controller;

    private Users mockUser;
    private SalesforceRecordsResponse<Users> mockResponse;

    @BeforeEach
    void setUp() {
        mockUser = new Users();
        mockUser.setId("003XX000004TmiQQAS");
        mockUser.setName("Test User");
        mockUser.setEmail("<EMAIL>");
        mockUser.setUsername("<EMAIL>");
        mockUser.setIsActive(true);

        mockResponse = new SalesforceRecordsResponse<>();
        mockResponse.setTotalSize(1);
        mockResponse.setDone(true);
        mockResponse.setRecords(Arrays.asList(mockUser));
    }

    @Test
    void getCrescendoUserByEmail_Success() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        when(crescendoUserService.getByUserEmail(email)).thenReturn(mockResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertNotNull(result.getBody().getData());
        assertEquals(1, result.getBody().getData().getTotalSize());
        assertEquals(1, result.getBody().getData().getRecords().size());
        assertEquals(email, result.getBody().getData().getRecords().get(0).getEmail());
        assertTrue(result.getBody().getData().getRecords().get(0).getIsActive());

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_WithValidEmail() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        mockUser.setEmail(email);
        when(crescendoUserService.getByUserEmail(email)).thenReturn(mockResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(Objects.requireNonNull(result.getBody()).getData());
        assertEquals(email, result.getBody().getData().getRecords().get(0).getEmail());

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_UserNotFound() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        when(crescendoUserService.getByUserEmail(email))
                .thenThrow(new CustomDEExceptions("User not found on Crescendo"));

        // Act & Assert
        assertThrows(CustomDEExceptions.class, () -> {
            controller.getCrescendoUserByEmail(email);
        });

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_MultipleUsersFound() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        when(crescendoUserService.getByUserEmail(email))
                .thenThrow(new CustomDEExceptions("This email has more than one registration on Crecendo"));

        // Act & Assert
        assertThrows(CustomDEExceptions.class, () -> {
            controller.getCrescendoUserByEmail(email);
        });

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_InactiveUser() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        when(crescendoUserService.getByUserEmail(email))
                .thenThrow(new CustomDEExceptions("This User is Inactive on Crescendo, please activate to Proceed with sync"));

        // Act & Assert
        assertThrows(CustomDEExceptions.class, () -> {
            controller.getCrescendoUserByEmail(email);
        });

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_EmptyEmail() throws CustomDEExceptions {
        // Arrange
        String email = "";
        when(crescendoUserService.getByUserEmail(email)).thenReturn(mockResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_DefaultValue() throws CustomDEExceptions {
        // Test the default value behavior when no email parameter is provided
        // This tests the @RequestParam defaultValue = ""
        
        // Arrange
        String defaultEmail = "";
        when(crescendoUserService.getByUserEmail(defaultEmail)).thenReturn(mockResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(defaultEmail);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        verify(crescendoUserService).getByUserEmail(defaultEmail);
    }

    @Test
    void getCrescendoUserByEmail_WithSpecialCharacters() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        mockUser.setEmail(email);
        when(crescendoUserService.getByUserEmail(email)).thenReturn(mockResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(email, Objects.requireNonNull(result.getBody()).getData().getRecords().get(0).getEmail());

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_CaseInsensitive() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        mockUser.setEmail(email.toLowerCase());
        when(crescendoUserService.getByUserEmail(email)).thenReturn(mockResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_LongEmail() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        mockUser.setEmail(email);
        when(crescendoUserService.getByUserEmail(email)).thenReturn(mockResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(email, Objects.requireNonNull(result.getBody()).getData().getRecords().get(0).getEmail());

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_ServiceReturnsNull() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        when(crescendoUserService.getByUserEmail(email)).thenReturn(null);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNull(Objects.requireNonNull(result.getBody()).getData());

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_MultipleRecords() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        Users secondUser = new Users();
        secondUser.setId("003XX000004TmiQQAT");
        secondUser.setName("Second User");
        secondUser.setEmail(email);
        secondUser.setIsActive(true);

        SalesforceRecordsResponse<Users> multipleUsersResponse = new SalesforceRecordsResponse<>();
        multipleUsersResponse.setTotalSize(2);
        multipleUsersResponse.setDone(true);
        multipleUsersResponse.setRecords(Arrays.asList(mockUser, secondUser));

        when(crescendoUserService.getByUserEmail(email)).thenReturn(multipleUsersResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(2, Objects.requireNonNull(result.getBody()).getData().getTotalSize());
        assertEquals(2, result.getBody().getData().getRecords().size());

        verify(crescendoUserService).getByUserEmail(email);
    }

    @Test
    void getCrescendoUserByEmail_ResponseEntityStructure() throws CustomDEExceptions {
        // Arrange
        String email = "<EMAIL>";
        when(crescendoUserService.getByUserEmail(email)).thenReturn(mockResponse);

        // Act
        ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>> result = 
                controller.getCrescendoUserByEmail(email);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getBody());
        assertTrue(result.getBody() instanceof ResponseEntityDto);
        assertNotNull(result.getBody().getData());
        assertTrue(result.getBody().getData() instanceof SalesforceRecordsResponse);

        verify(crescendoUserService).getByUserEmail(email);
    }
}
