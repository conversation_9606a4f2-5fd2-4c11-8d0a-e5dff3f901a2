/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.dto.UserJourneyDto;
import com.app.cargill.dto.UserJourneyPathDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.service.IUserJourneyService;
import com.app.cargill.utils.ExcelUtils;
import java.io.IOException;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserJourneyExcelReportServiceImpl implements IExcelReportService {

  private final IUserJourneyService userJourneyService;

  @Override
  public String getFileName(Object data) {
    return "UserJourneyAnalytics_" + Instant.now().getEpochSecond() + ".xlsx";
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    
    Instant lastUpdatedDate = (Instant) data;
    log.debug("Preparing Excel export for user journey data updated after: {}", lastUpdatedDate);
    
    List<UserJourneyDto> userJourneys = userJourneyService.getByUpdatedDateAfter(lastUpdatedDate);
    
    try (XSSFWorkbook workbook = new XSSFWorkbook()) {
      XSSFSheet sheet = workbook.createSheet("User Journey Analytics");
      
      // Create cell styles
      XSSFCellStyle headerStyle = ExcelUtils.applyCellStyle(
          workbook,
          IndexedColors.GREY_25_PERCENT,
          org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND,
          HorizontalAlignment.CENTER,
          ExcelUtils.getFont(workbook, false, true, IndexedColors.BLACK));
      
      XSSFCellStyle dataStyle = ExcelUtils.applyCellStyle(
          workbook,
          null,
          null,
          HorizontalAlignment.LEFT,
          ExcelUtils.getFont(workbook, false, false, IndexedColors.BLACK));
      
      AtomicInteger rowNumber = new AtomicInteger(0);
      
      // Create header row
      createHeaderRow(sheet, headerStyle, rowNumber);
      
      // Create data rows
      createDataRows(sheet, dataStyle, userJourneys, rowNumber);
      
      return ExcelUtils.finalizeWorkbook(workbook, 4); // 4 columns
      
    } catch (IOException e) {
      log.error("Error creating Excel file for user journey data", e);
      throw new IOException("Error creating Excel file: " + e.getMessage());
    }
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale)
      throws IOException, URISyntaxException {
    // Not implemented for user journey analytics
    throw new UnsupportedOperationException("Image export not supported for user journey analytics");
  }

  public ByteArrayResource prepareExportToExcel(
      Instant lastUpdatedDate, ResourceBundleMessageSource source, Locale locale) throws IOException {
    return prepareExportToExcel((Object) lastUpdatedDate, source, locale);
  }

  private void createHeaderRow(XSSFSheet sheet, XSSFCellStyle headerStyle, AtomicInteger rowNumber) {
    XSSFRow headerRow = sheet.createRow(rowNumber.getAndIncrement());
    AtomicInteger cellNumber = new AtomicInteger(0);
    
    ExcelUtils.createAndSetCellValue(headerRow, cellNumber, headerStyle, "User Email");
    ExcelUtils.createAndSetCellValue(headerRow, cellNumber, headerStyle, "Event Name");
    ExcelUtils.createAndSetCellValue(headerRow, cellNumber, headerStyle, "Path");
    ExcelUtils.createAndSetCellValue(headerRow, cellNumber, headerStyle, "Event Trigger Time");
  }

  private void createDataRows(
      XSSFSheet sheet, 
      XSSFCellStyle dataStyle, 
      List<UserJourneyDto> userJourneys, 
      AtomicInteger rowNumber) {
    
    for (UserJourneyDto userJourney : userJourneys) {
      if (userJourney.getPaths() != null) {
        for (UserJourneyPathDto path : userJourney.getPaths()) {
          XSSFRow dataRow = sheet.createRow(rowNumber.getAndIncrement());
          AtomicInteger cellNumber = new AtomicInteger(0);
          
          ExcelUtils.createAndSetCellValue(dataRow, cellNumber, dataStyle, userJourney.getUserEmail());
          ExcelUtils.createAndSetCellValue(dataRow, cellNumber, dataStyle, path.getEventName());
          ExcelUtils.createAndSetCellValue(dataRow, cellNumber, dataStyle, path.getPath());
          ExcelUtils.createAndSetCellValue(dataRow, cellNumber, dataStyle, path.getEventTriggerTime());
        }
      }
    }
  }
}
