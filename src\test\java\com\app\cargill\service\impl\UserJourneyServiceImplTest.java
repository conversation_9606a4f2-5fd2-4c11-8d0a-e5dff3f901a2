/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.UserJourneyDocument;
import com.app.cargill.document.UserJourneyPath;
import com.app.cargill.dto.UserJourneyDto;
import com.app.cargill.dto.UserJourneyPathDto;
import com.app.cargill.dto.UserJourneyRequestDto;
import com.app.cargill.model.UserJourney;
import com.app.cargill.repository.UserJourneyRepository;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

@ExtendWith(MockitoExtension.class)
class UserJourneyServiceImplTest {

  @Mock
  private UserJourneyRepository userJourneyRepository;

  @Mock
  private ModelMapper modelMapper;

  @InjectMocks
  private UserJourneyServiceImpl userJourneyService;

  private UserJourneyRequestDto testRequestDto;
  private UserJourney testUserJourney;

  @BeforeEach
  void setUp() {
    // Create test data
    UserJourneyPathDto pathDto1 = UserJourneyPathDto.builder()
        .eventName("DASHBOARD_NOTES")
        .path("dashboard/notes")
        .eventTriggerTime("12-15-20")
        .build();

    UserJourneyPathDto pathDto2 = UserJourneyPathDto.builder()
        .eventName("CUSTOMER_SITES_NOTES")
        .path("dashboard/customers/sites/notes")
        .eventTriggerTime("12-15-20")
        .build();

    testRequestDto = UserJourneyRequestDto.builder()
        .userEmail("<EMAIL>")
        .paths(Arrays.asList(pathDto1, pathDto2))
        .build();

    // Create test entity
    UserJourneyPath path1 = UserJourneyPath.builder()
        .eventName("DASHBOARD_NOTES")
        .path("dashboard/notes")
        .eventTriggerTime("12-15-20")
        .build();

    UserJourneyPath path2 = UserJourneyPath.builder()
        .eventName("CUSTOMER_SITES_NOTES")
        .path("dashboard/customers/sites/notes")
        .eventTriggerTime("12-15-20")
        .build();

    UserJourneyDocument document = UserJourneyDocument.builder()
        .id(UUID.randomUUID())
        .userEmail("<EMAIL>")
        .paths(Arrays.asList(path1, path2))
        .createTimeUtc(Instant.now())
        .lastModifiedTimeUtc(Instant.now())
        .isNew(true)
        .isDeleted(false)
        .build();

    testUserJourney = UserJourney.builder()
        .id(1L)
        .userJourneyDocument(document)
        .createdDate(new Date())
        .updatedDate(new Date())
        .deleted(false)
        .build();
  }

  @Test
  void testSave_Success() {
    // Given
    when(userJourneyRepository.save(any(UserJourney.class))).thenReturn(testUserJourney);

    // When
    UserJourneyDto result = userJourneyService.save(testRequestDto);

    // Then
    assertNotNull(result);
    assertEquals("<EMAIL>", result.getUserEmail());
    assertEquals(2, result.getPaths().size());
  }

  @Test
  void testGetByUpdatedDateAfter_Success() {
    // Given
    Instant lastUpdatedDate = Instant.now().minusSeconds(3600);
    List<UserJourney> mockUserJourneys = Arrays.asList(testUserJourney);
    
    when(userJourneyRepository.findByUpdatedDateAfter(lastUpdatedDate)).thenReturn(mockUserJourneys);

    // When
    List<UserJourneyDto> result = userJourneyService.getByUpdatedDateAfter(lastUpdatedDate);

    // Then
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("<EMAIL>", result.get(0).getUserEmail());
  }
}
