/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserJourneyPath implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Path")
  private String path;

  @JsonProperty("EventName")
  private String eventName;

  @JsonProperty("EventTriggerTime")
  private String eventTriggerTime;
}
