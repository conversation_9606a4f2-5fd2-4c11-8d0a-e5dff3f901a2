/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.config.CrescendoConfig;
import com.app.cargill.exceptions.ExceptionInstanceValidator;
import com.app.cargill.sf.cc.api.model.VersionObject;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.service.SalesforceClientFactory;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.Serializable;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

@ExtendWith(MockitoExtension.class)
class CrescendoApiReactiveServiceTest {

    @Mock
    private CrescendoConfig crescendoConfig;

    @Mock
    private SalesforceClientFactory clientFactory;

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestBodySpec requestBodySpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @Mock
    private ClientResponse clientResponse;

    @InjectMocks
    private CrescendoApiReactiveService crescendoApiReactiveService;

    private AuthToken mockAuthToken;
    private VersionObject mockVersionObject;

    @BeforeEach
    void setUp() {
        mockAuthToken = new AuthToken();
        mockAuthToken.setAccessToken("test-access-token");
        mockAuthToken.setInstanceUrl("https://test.salesforce.com");
        mockAuthToken.setIssuedAt(System.currentTimeMillis());

        mockVersionObject = VersionObject.builder()
                .version("58.0")
                .url("/services/data/v58.0")
                .label("Winter '24")
                .build();

        // Setup common config mocks
        when(crescendoConfig.getScheme()).thenReturn("https");
        when(crescendoConfig.getTokenHost()).thenReturn("test.salesforce.com");
        when(crescendoConfig.getTokenPath()).thenReturn("/services/oauth2/token");
        when(crescendoConfig.getPort()).thenReturn("443");
        when(crescendoConfig.getTokenAuthHeader()).thenReturn("Basic dGVzdDp0ZXN0");
        when(crescendoConfig.getGrantType()).thenReturn("client_credentials");
    }

    @Test
    void getToken_Success() {
        // Arrange
        when(clientFactory.createClient()).thenReturn(webClient);
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(any(Function.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.header(anyString(), anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.exchangeToMono(any(Function.class))).thenReturn(Mono.just(mockAuthToken));

        // Act & Assert
        StepVerifier.create(crescendoApiReactiveService.getToken())
                .expectNext(mockAuthToken)
                .verifyComplete();

        verify(clientFactory).createClient();
        verify(webClient).post();
    }

    @Test
    void getToken_ErrorResponse() {
        // Arrange
        when(clientFactory.createClient()).thenReturn(webClient);
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(any(Function.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.header(anyString(), anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any())).thenReturn(requestHeadersSpec);
        
        WebClientResponseException exception = WebClientResponseException.create(
                HttpStatus.UNAUTHORIZED.value(), 
                "Unauthorized", 
                HttpHeaders.EMPTY, 
                new byte[0], 
                null
        );
        
        when(requestHeadersSpec.exchangeToMono(any(Function.class))).thenReturn(Mono.error(exception));

        // Act & Assert
        StepVerifier.create(crescendoApiReactiveService.getToken())
                .expectError(WebClientResponseException.class)
                .verify();
    }

    @Test
    void getToken_WithRetry_UnknownHostException() {
        // Arrange
        when(clientFactory.createClient()).thenReturn(webClient);
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(any(Function.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.header(anyString(), anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any())).thenReturn(requestHeadersSpec);
        
        UnknownHostException unknownHostException = new UnknownHostException("Host not found");
        
        // Mock the ExceptionInstanceValidator to return true for UnknownHostException
        try (var mockedStatic = mockStatic(ExceptionInstanceValidator.class)) {
            mockedStatic.when(() -> ExceptionInstanceValidator.isUnknownHostExceptionError(any()))
                    .thenReturn(true);
            
            when(requestHeadersSpec.exchangeToMono(any(Function.class)))
                    .thenReturn(Mono.error(unknownHostException))
                    .thenReturn(Mono.error(unknownHostException))
                    .thenReturn(Mono.error(unknownHostException))
                    .thenReturn(Mono.just(mockAuthToken));

            // Act & Assert
            StepVerifier.create(crescendoApiReactiveService.getToken())
                    .expectNext(mockAuthToken)
                    .verifyComplete();
        }
    }

    @Test
    void getApiVersions_Success() {
        // Arrange
        List<VersionObject> versions = Arrays.asList(
                VersionObject.builder().version("57.0").url("/services/data/v57.0").build(),
                VersionObject.builder().version("58.0").url("/services/data/v58.0").build()
        );

        when(clientFactory.createClient(mockAuthToken.getInstanceUrl())).thenReturn(webClient);
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(any(Function.class))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(VersionObject.class)).thenReturn(Flux.fromIterable(versions));

        // Act & Assert
        StepVerifier.create(crescendoApiReactiveService.getApiVersions(mockAuthToken))
                .expectNext(versions.get(0))
                .expectNext(versions.get(1))
                .verifyComplete();

        verify(clientFactory).createClient(mockAuthToken.getInstanceUrl());
    }

    @Test
    void getLatestApiVersion_Success() {
        // Arrange
        List<VersionObject> versions = Arrays.asList(
                VersionObject.builder().version("57.0").url("/services/data/v57.0").build(),
                VersionObject.builder().version("58.0").url("/services/data/v58.0").build(),
                VersionObject.builder().version("56.0").url("/services/data/v56.0").build()
        );

        when(clientFactory.createClient(mockAuthToken.getInstanceUrl())).thenReturn(webClient);
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(any(Function.class))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(VersionObject.class)).thenReturn(Flux.fromIterable(versions));

        // Act & Assert
        StepVerifier.create(crescendoApiReactiveService.getLatestApiVersion(mockAuthToken))
                .expectNext(versions.get(1)) // Should return version 58.0 as it's the highest
                .verifyComplete();
    }

    @Test
    void getRecordsQuery_Success() {
        // Arrange
        String apiPath = "/services/data/v58.0";
        String query = "SELECT Id, Name FROM User";
        ParameterizedTypeReference<SalesforceRecordsResponse<TestRecord>> typeReference = 
                new ParameterizedTypeReference<>() {};

        TestRecord testRecord = new TestRecord("123", "Test User");
        SalesforceRecordsResponse<TestRecord> response = new SalesforceRecordsResponse<>();
        response.setTotalSize(1);
        response.setDone(true);
        response.setRecords(Arrays.asList(testRecord));

        when(clientFactory.createClient(mockAuthToken.getInstanceUrl())).thenReturn(webClient);
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(any(Function.class))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.exchangeToMono(any(Function.class))).thenReturn(Mono.just(response));

        // Act & Assert
        StepVerifier.create(crescendoApiReactiveService.getRecordsQuery(mockAuthToken, apiPath, query, typeReference))
                .expectNext(response)
                .verifyComplete();

        verify(clientFactory).createClient(mockAuthToken.getInstanceUrl());
    }

    @Test
    void getRecordsQuery_ErrorResponse() {
        // Arrange
        String apiPath = "/services/data/v58.0";
        String query = "SELECT Id, Name FROM User";
        ParameterizedTypeReference<SalesforceRecordsResponse<TestRecord>> typeReference = 
                new ParameterizedTypeReference<>() {};

        when(clientFactory.createClient(mockAuthToken.getInstanceUrl())).thenReturn(webClient);
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(any(Function.class))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
        
        WebClientResponseException exception = WebClientResponseException.create(
                HttpStatus.BAD_REQUEST.value(), 
                "Bad Request", 
                HttpHeaders.EMPTY, 
                new byte[0], 
                null
        );
        
        when(requestHeadersSpec.exchangeToMono(any(Function.class))).thenReturn(Mono.error(exception));

        // Act & Assert
        StepVerifier.create(crescendoApiReactiveService.getRecordsQuery(mockAuthToken, apiPath, query, typeReference))
                .expectError(WebClientResponseException.class)
                .verify();
    }

    // Test record class for testing purposes
    private static class TestRecord implements Serializable {
        private String id;
        private String name;

        public TestRecord(String id, String name) {
            this.id = id;
            this.name = name;
        }

        public String getId() { return id; }
        public String getName() { return name; }
    }
}
