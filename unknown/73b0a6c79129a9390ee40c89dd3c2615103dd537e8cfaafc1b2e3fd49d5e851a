/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.UserJourneyDto;
import com.app.cargill.dto.UserJourneyRequestDto;
import com.app.cargill.service.IUserJourneyService;
import com.app.cargill.service.impl.UserJourneyExcelReportServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.Instant;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/user-journey-analytics")
@Tag(
    name = "User Journey Analytics Controller",
    description = "Endpoints for user journey analytics data management and export")
@RequiredArgsConstructor
@Slf4j
public class UserJourneyAnalyticsController {

  private final IUserJourneyService userJourneyService;
  private final UserJourneyExcelReportServiceImpl userJourneyExcelReportService;
  private final ResourceBundleMessageSource resourceBundleMessageSource;

  @PostMapping
  @Operation(
      summary = "Save User Journey Data",
      description = "This API will save user journey analytics data from mobile app")
  public ResponseEntity<ResponseEntityDto<UserJourneyDto>> saveUserJourney(
      @Valid @RequestBody UserJourneyRequestDto userJourneyRequestDto) {
    try {
      log.info("Saving user journey data for user: {}", userJourneyRequestDto.getUserEmail());
      UserJourneyDto savedData = userJourneyService.save(userJourneyRequestDto);
      
      return ResponseEntity.ok(
          ResponseEntityDto.<UserJourneyDto>builder()
              .data(savedData)
              .message("User journey data saved successfully")
              .status(ResponseStatus.SUCCESS)
              .build());
    } catch (Exception e) {
      log.error("Error saving user journey data", e);
      return ResponseEntity.internalServerError()
          .body(
              ResponseEntityDto.<UserJourneyDto>builder()
                  .message("Error saving user journey data: " + e.getMessage())
                  .status(ResponseStatus.FAILED)
                  .build());
    }
  }

  @GetMapping("/export")
  @Operation(
      summary = "Export User Journey Data to Excel",
      description = "This API will export user journey analytics data to Excel file based on last updated date")
  public ResponseEntity<ByteArrayResource> exportUserJourneyToExcel(
      @RequestParam("lastUpdatedDate") Instant lastUpdatedDate,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws IOException {
    try {
      log.info("Exporting user journey data updated after: {}", lastUpdatedDate);
      
      Locale locale = Locale.forLanguageTag(localeString);
      LocaleContextHolder.setLocale(locale);
      
      ByteArrayResource byteArrayResource = 
          userJourneyExcelReportService.prepareExportToExcel(lastUpdatedDate, resourceBundleMessageSource, locale);
      
      String fileName = "UserJourneyAnalytics_" + Instant.now().getEpochSecond() + ".xlsx";
      
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setContentDispositionFormData("attachment", fileName);
      
      return new ResponseEntity<>(byteArrayResource, headers, HttpStatus.OK);
    } catch (Exception e) {
      log.error("Error exporting user journey data", e);
      throw new IOException("Error exporting user journey data: " + e.getMessage());
    }
  }
}
