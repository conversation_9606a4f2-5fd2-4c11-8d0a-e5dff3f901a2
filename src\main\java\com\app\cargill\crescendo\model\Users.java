package com.app.cargill.crescendo.model;

import java.io.Serial;
import java.io.Serializable;

import com.app.cargill.sf.cc.model.RecordAttributes;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Users implements Serializable {/**
	 * 
	 */
	  @Serial private static final long serialVersionUID = 1L;

	  @JsonProperty("attributes")
	  private RecordAttributes attributes;

	  @JsonProperty("Id")
	  private String id;

	  @JsonProperty("Name")
	  private String name;

	  @JsonProperty("Email")
	  private String email;


	  @JsonProperty("Username")
	  private String username;
	  
	  @JsonProperty("IsActive")
	  private Boolean isActive;
	
}
