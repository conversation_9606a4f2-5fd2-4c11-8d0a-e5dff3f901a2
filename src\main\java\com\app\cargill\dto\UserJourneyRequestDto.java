/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserJourneyRequestDto implements Serializable {

  private static final long serialVersionUID = 1L;

  private String userEmail;
  private List<UserJourneyPathDto> paths;
}
